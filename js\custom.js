$(document).ready(function() {
  $('.slidersec').owlCarousel({
    loop: true,
    margin: 0,
    responsiveClass: true,
    autoplay: false,
    nav: false,
    dots: true,
    responsive: {
      0: {
        items: 1
      },
      600: {
        items: 1
      },
      1000: {
        items: 1
      }
    }
  })
})

$(document).ready(function() {
$('.tabslidersec').owlCarousel({
  loop: true,
  margin: 0,
  responsiveClass: true,
  autoplay: false,
  nav: true,
  dots: true,
  animateOut: 'slideOutUp',
  animateIn: 'slideInUp',
  responsive: {
    0: {
      items: 1
    },
    768: {
      items: 1
    },
    992: {
      items: 1
    }
  }
})
})

$(document).ready(function() {
$('.industryleadercareosel').owlCarousel({
  loop: true,
  margin: 0,
  responsiveClass: true,
  autoplay: true,
  autoplayTimeout: 5000,
  autoplayHoverPause: false,
  nav: false,
  dots: true,
  animateOut: 'slideOutLeft',
  animateIn: 'slideInRight',
  responsive: {
    0: {
      items: 1
    },
    768: {
      items: 1
    },
    992: {
      items: 1
    }
  }
})
})

$(document).ready(function() {
$('.customervoice').owlCarousel({
  loop: true,
  margin: 20,
  responsiveClass: true,
  autoplay: true,
  nav: false,
  dots: true,
  responsive: {
    0: {
      items: 1
    },
    768: {
      items: 2
    },
    992: {
      items: 3
    },
    1025: {
      items: 4
    }
  }
})
})

$(document).ready(function() {
$('.foursegmentsec').owlCarousel({
loop: true,
margin: 20,
responsiveClass: true,
autoplay: true,
nav: true,
dots: false,
responsive: {
  0: {
    items: 1
  },
  480: {
    items: 2
  },
  768: {
    items: 3
  },
  992: {
    items: 4
  }
}
})
})

$(document).ready(function() {
$('.btmbannerfacilityslider').owlCarousel({
loop: true,
margin: 0,
responsiveClass: true,
autoplay: true,
nav: false,
dots: true,
responsive: {
  0: {
    items: 1
  },
  480: {
    items: 1
  },
  768: {
    items: 1
  }
}
})
})

$(document).ready(function() {
$('.facilitydesignsec').owlCarousel({
loop: true,
margin: 20,
responsiveClass: true,
autoplay: true,
nav: false,
dots: true,
responsive: {
  0: {
    items: 1
  },
  480: {
    items: 2
  },
  768: {
    items: 3
  }
}
})
})

$(document).ready(function() {
$('.teamexpertcarousel').owlCarousel({
loop: true,
margin: 20,
responsiveClass: true,
autoplay: true,
nav: false,
dots: true,
responsive: {
  0: {
    items: 1
  },
  480: {
    items: 1
  },
  768: {
    items: 2
  },
  992: {
    items: 3
  }
}
})
})

function openCity(evt, cityName) {
var i, tabcontent, tablinks;
tabcontent = document.getElementsByClassName("tabcontent");
for (i = 0; i < tabcontent.length; i++) {
  tabcontent[i].style.display = "none";
}
tablinks = document.getElementsByClassName("tablinks");
for (i = 0; i < tablinks.length; i++) {
  tablinks[i].className = tablinks[i].className.replace(" active", "");
}
document.getElementById(cityName).style.display = "block";
evt.currentTarget.className += " active";
}

function valueopenCity(valueevt, valuecityName) {
var a, valuetabcontent, valuetablinks;
valuetabcontent = document.getElementsByClassName("valuetabcontent");
for (a = 0; a < valuetabcontent.length; a++) {
valuetabcontent[a].style.display = "none";
}
valuetablinks = document.getElementsByClassName("valuetablinks");
for (a = 0; a < valuetablinks.length; a++) {
valuetablinks[a].className = valuetablinks[a].className.replace(" active", "");
}
document.getElementById(valuecityName).style.display = "block";
valueevt.currentTarget.className += " active";
}

$('.slimmenu').slimmenu(
{
  resizeWidth: '991',
  collapserTitle: '',
  animSpeed:'medium',
  indentChildren: true,
  childrenIndenter: '&raquo;'
});