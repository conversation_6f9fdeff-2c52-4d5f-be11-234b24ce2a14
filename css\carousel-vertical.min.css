/*!
 * carousel vertical
 * Copyright 2011-2019 <PERSON>
 * Licensed under MIT (https://github.com/iannacone/carousel-vertical/blob/master/LICENSE)
 */.cv-carousel{display:block;height:100%;z-index:1}.cv-carousel.cv-grab{cursor:-webkit-grab;cursor:grab}.cv-carousel.cv-drag .cv-item{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.cv-carousel.cv-drag .cv-item .item{height:100%}.cv-carousel .cv-stage-outer{position:relative;overflow:hidden;height:100%;-webkit-transform:translate3d(0, 0, 0);transform:translate3d(0, 0, 0)}.cv-carousel .cv-stage{position:relative;-ms-touch-action:pan-Y;touch-action:pan-Y;-webkit-backface-visibility:hidden;backface-visibility:hidden;-webkit-transition:all .25s ease;transition:all .25s ease}.cv-carouse<PERSON>,.cv-carousel .cv-item{position:relative;-webkit-tap-highlight-color:transparent}.cv-carousel .cv-item,.cv-carousel .cv-wrapper{-webkit-backface-visibility:hidden;backface-visibility:hidden;-webkit-transform:translate3d(0, 0, 0);transform:translate3d(0, 0, 0)}.cv-carousel .cv-item{width:100%;min-height:1px;-webkit-backface-visibility:hidden;backface-visibility:hidden;-webkit-touch-callout:none}.cv-carousel .cv-nav{float:left;clear:left;position:relative;top:50%;margin-right:.625rem;-webkit-transform:translateY(-50%);transform:translateY(-50%)}.cv-carousel .cv-nav .cv-prev,.cv-carousel .cv-nav .cv-next{margin:.3125rem;padding:.25rem .4375rem;font-size:.875rem;background:#D6D6D6;color:#000;border-radius:.1875rem;cursor:pointer}
/*# sourceMappingURL=carousel-vertical.min.css.map */